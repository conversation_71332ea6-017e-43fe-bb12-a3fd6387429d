import { createHmac } from 'crypto';
import { PatientMessageRouterService } from '@modules/chat/services/patient-message-router.service';
import {
  Body,
  Controller,
  Headers,
  HttpCode,
  Logger,
  Post,
  RawBodyRequest,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { IntercomWebhookDto } from './dto/webhook.dto';
import { IntercomConversationEvent } from './types/webhook';
import { IntercomConversationEventUseCase } from './use-cases/intercom-conversation-event.use-case';

@Controller('intercom')
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly patientMessageRouterService: PatientMessageRouterService,
    private readonly intercomConversationEventUseCase: IntercomConversationEventUseCase,
  ) {}

  @Post('webhook')
  @HttpCode(200)
  async handleIntercomWebhook(
    @Body() payload: IntercomWebhookDto,
    @Req() req: RawBodyRequest<Request>,
    @Headers('X-Hub-Signature') signature: string,
  ) {
    //this.validateWebhookSignature(signature, JSON.stringify(payload));
    const rawBody = req.rawBody;
    this.validateWebhookSignature(signature, rawBody);

    try {
      // Log the received webhook for debugging
      this.logger.debug(
        `Received Intercom webhook: ${JSON.stringify(payload)}`,
      );

      // Extract the event type from the payload
      const { topic } = payload;

      switch (topic) {
        case 'ping':
          // Handle ping event
          this.logger.log(
            `Received ping from Intercom: ${payload.data.item.message}`,
          );
          break;
        case 'conversation.admin.single.created':
        case 'conversation.admin.replied':
        case 'conversation.user.created':
        case 'conversation.user.replied':
          await this.intercomConversationEventUseCase.execute(payload);
          break;
        case 'conversation.admin.closed':
          // Handle conversation closed event
          try {
            const conversationEvent = payload as IntercomConversationEvent;
            const conversationId = conversationEvent.data?.item?.source?.id;

            if (!conversationId) {
              this.logger.warn(
                'Received conversation.admin.closed event without conversation ID',
              );
              break;
            }

            this.logger.log(`Intercom conversation closed: ${conversationId}`);
            await this.patientMessageRouterService.markConversationRouterAsClosed(
              conversationId,
            );
          } catch (error) {
            this.logger.error(
              `Error processing conversation.admin.closed event: ${error.message}`,
              error.stack,
            );
          }
          break;
        case 'conversation.admin.opened':
          // Handle conversation reopened event
          try {
            const conversationEvent = payload as IntercomConversationEvent;
            const conversationId = conversationEvent.data?.item?.source?.id;

            if (!conversationId) {
              this.logger.warn(
                'Received conversation.admin.opened event without conversation ID',
              );
              break;
            }

            this.logger.log(
              `Intercom conversation reopened: ${conversationId}`,
            );
            await this.patientMessageRouterService.reopenClosedConversationRouter(
              conversationId,
            );
          } catch (error) {
            this.logger.error(
              `Error processing conversation.admin.opened event: ${error.message}`,
              error.stack,
            );
          }
          break;
        default:
          this.logger.warn(
            `Received unhandled Intercom webhook topic: ${topic}`,
          );
      }

      // Return a successful response to acknowledge receipt
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      // Log the error but still return a 200 response for business logic errors
      // This allows us to handle errors internally without causing webhook retries
      this.logger.error(
        `Error processing Intercom webhook: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: 'Error processing webhook, but acknowledged',
      };
    }
  }

  validateWebhookSignature(signature: string, rawBody: Buffer): void {
    // If webhook security is configured, validate the signature
    const clientSecret = this.configService.get<string>(
      'INTERCOM_CLIENT_SECRET',
    );

    if (!clientSecret) {
      this.logger.warn('Intercom client secret not configured');
      return;
    }

    // Intercom signatures start with 'sha1='
    if (!signature.startsWith('sha1=')) {
      this.logger.error(`Invalid signature format: ${signature}`);
      throw new UnauthorizedException('Invalid signature format');
    }

    // Extract the actual signature (remove 'sha1=' prefix)
    const actualSignature = signature.substring(5);

    // Calcular el signature igual que el ejemplo Node.js
    // Usar el clientSecret como string y el rawBody como string
    const hmac = createHmac('sha1', clientSecret);
    hmac.update(
      typeof rawBody === 'string' ? rawBody : rawBody.toString('utf8'),
    );
    const expectedSignature = hmac.digest('hex');

    // Log para depuración: mostrar la firma esperada y la recibida
    this.logger.debug(`Expected signature: ${expectedSignature}`);
    this.logger.debug(`Actual signature: ${actualSignature}`);

    // Log para depuración: mostrar el rawBody como string y su longitud
    const rawBodyString = typeof rawBody === 'string' ? rawBody : rawBody.toString('utf8');
    this.logger.debug(`Raw body for HMAC: ${rawBodyString}`);
    this.logger.debug(`Raw body length: ${rawBodyString.length}`);

    // Log para depuración: mostrar el body original esperado (puedes copiar el body aquí para comparar)
    const originalBody = '{"type":"notification_event","app_id":"ivt1ozil","data":{"type":"notification_event_data","item":{"type":"ping","message":"This is a ping notification test message."}},"links":{},"id":null,"topic":"ping","delivery_status":null,"delivery_attempts":1,"delivered_at":0,"first_sent_at":1753195597,"created_at":1753195597,"self":null}';
    this.logger.debug(`Original body for comparison: ${originalBody}`);
    this.logger.debug(`Original body length: ${originalBody.length}`);
    this.logger.debug(`Bodies are equal: ${rawBodyString === originalBody}`);

    // Compare signatures
    if (actualSignature !== expectedSignature) {
      this.logger.error('Webhook signature validation failed');
      this.logger.error(
        `Signature mismatch. Expected: ${expectedSignature}, Actual: ${actualSignature}`,
      );
      throw new UnauthorizedException('Invalid webhook signature');
    }
  }
}
